package ca.bnc.payment.controller;

import ca.bnc.payment.model.PaymentSimulationHeader;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.ChannelType;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.model.SendPaymentExecuteRequest;
import ca.bnc.payment.pmt_etransfer_payment_processing_simulation_api_resources.generated.rest.EtPmtProcApi;
import ca.bnc.payment.service.PaymentSimulationOrchestrator;
import ca.nbc.payment.lib.service.logging.LogContextHolder;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.UUID;

@RestController
@RequiredArgsConstructor
@Validated
public class PaymentProcessingSimulationController implements EtPmtProcApi {

    private final PaymentSimulationOrchestrator paymentSimulationOrchestrator;
    private final LogContextHolder logContextHolder;
    private final LogContextHelper logContextHelper;

    @Override
    public ResponseEntity<Object> simulateDomesticOutgoingPayment(final String endToEndBusinessIdentification,
                                                                  final String xChannelId,
                                                                  final ChannelType xChannelType,
                                                                  final String xClientId,
                                                                  final String accept,
                                                                  final UUID xRequestId,
                                                                  final String traceparent,
                                                                  final String bncBusinessTraceId,
                                                                  final SendPaymentExecuteRequest sendPaymentExecuteRequest,
                                                                  final String tracestate,
                                                                  final String xClientAgentId,
                                                                  final String xAgentId) {
        return logContextHolder.runWithContextNoReset(
                () -> {
                    final PaymentSimulationHeader paymentSimulationHeader = new PaymentSimulationHeader(
                            traceparent,
                            xRequestId,
                            xChannelId,
                            xChannelType,
                            xClientId,
                            bncBusinessTraceId,
                            tracestate
                    );
                    paymentSimulationOrchestrator.simulatePayment(
                            sendPaymentExecuteRequest,
                            endToEndBusinessIdentification,
                            paymentSimulationHeader,
                            accept
                    );
                    return ResponseEntity.status(HttpStatus.ACCEPTED).body(Collections.emptyMap());
                },
                logContextHelper.contextFor(endToEndBusinessIdentification, xRequestId, xClientId)
        );
    }
}
