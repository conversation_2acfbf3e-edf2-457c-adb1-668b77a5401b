package ca.bnc.payment.validator;

import ca.bnc.payment.exception.PaymentSimulationException;
import ca.bnc.payment.model.PaymentSimulationRequestContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static ca.bnc.payment.constant.ErrorConstants.*;
import static ca.bnc.payment.constant.ValidationOrderConstants.ONE;

/**
 * Validator to ensure the Accept header has the exact required value.
 * This validator enforces that the Accept header must be exactly 
 * 'application/vnd.ca.bnc.pmt+json;version=v1' as specified in the OpenAPI specification.
 */
@Component
@Order(ONE)
public class AcceptHeaderValidator implements PaymentRequestValidator {

    private static final String REQUIRED_ACCEPT_HEADER_VALUE = "application/vnd.ca.bnc.pmt+json;version=v1";

    @Override
    public void validate(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        final String acceptHeader = paymentSimulationRequestContext.acceptHeader();
        
        if (!REQUIRED_ACCEPT_HEADER_VALUE.equals(acceptHeader)) {
            throw PaymentSimulationException.badRequest(
                    REQUEST_INVALID_CODE,
                    String.format("Invalid Accept header. Expected: '%s', but received: '%s'", 
                            REQUIRED_ACCEPT_HEADER_VALUE, acceptHeader),
                    SERVICE_ORIGIN,
                    NA);
        }
    }

    @Override
    public boolean isApplicable(final PaymentSimulationRequestContext paymentSimulationRequestContext) {
        // This validation applies to all requests
        return true;
    }
}
