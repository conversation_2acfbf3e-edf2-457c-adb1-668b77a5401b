openapi: 3.0.3
info:
  version: 0.0.1
  title: eTransfer Payment Processing Simulation API Specification
  x-version-history:
    0.0.1: 2024-12-28 - First draft for outgoing eTransfer Payment Simulation API
  termsOfService: http://www.bnc.ca/
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html
  description: >-
    The eTransfer payment processing simulation API allows an application:
    Simulation of an eTransfer payment processing, in Synchronous mode. Simulation is a mandatory step to do 
    before the execution of a payment.
    scope: pmt:etransfer-payment-processing-simulation:write

  x-api-id: d6a473e0-e463-4abb-8a61-140d1b914ff0
  x-app-id: 6256
  x-audience:
    - internal
  contact:
    name: Support PAYPRO
    email: <EMAIL>
    url: https://jira.bnc.ca/servicedesk/customer/portal/1036/group/2308
servers:
  - url: https://pmt-tu.apis.bngf.local/
    description: TU Environment
    x-stage-id: tu
  - url: https://pmt-ti.apis.bngf.local
    description: TI server - Integration test server
    x-environment: non_production
    x-stage-id: ti
  - url: https://pmt-ta.apis.bngf.local
    description: TA server - Acceptance test server
    x-environment: non_production
    x-stage-id: ta
  - url: https://pmt.apis.bnc.ca
    description: prod server - official deployment - stable version only
    x-environment: production
    x-stage-id: production
tags:
  - name: eTransfer Payment Processing Simulation

paths:
  /et_pmt_proc/etransfer-payment-processing-simulation/{endToEndBusinessIdentification}:
    put:
      tags:
        - eTransfer Payment Processing Simulation
      summary: Simulate an eTransfer Payment Processing using ISO20022 (pacs.008) Message format.
      description: >-
        Simulate a new outgoing eTranfer payment processing, but it does not execute the actual payment (move money). 
      operationId: simulateDomesticOutgoingPayment
      security:
        - oAuth2:
            - pmt:simulation-processing:update
      parameters:
        - $ref: "#/components/parameters/Id"
        - $ref: "#/components/parameters/ChannelId"
        - $ref: "#/components/parameters/ChannelType"
        - $ref: "#/components/parameters/ClientId"
        - $ref: "#/components/parameters/Accept"
        - $ref: "#/components/parameters/RequestId"
        - $ref: "#/components/parameters/Traceparent"
        - $ref: '#/components/parameters/Bncbusinesstraceid'
        - $ref: '#/components/parameters/Tracestate'
        - $ref: "#/components/parameters/ClientAgentId"
        - $ref: "#/components/parameters/AgentId"
      requestBody:
        required: true
        content:
          application/vnd.ca.bnc.pmt+json:
            schema:
              $ref: '#/components/schemas/SendPaymentExecuteRequest'
      responses:
        '202': {"$ref": "#/components/responses/202-accepted"}
        '400': {"$ref": "#/components/responses/400-bad-request"}
        '403': {"$ref": "#/components/responses/403-forbidden"}
        '500': {"$ref": "#/components/responses/500-internal-server-error"}
        '503': { "$ref": "#/components/responses/503-service-unavailable" }

components:
  parameters:
    Id:
      name: endToEndBusinessIdentification
      in: path
      description: |
        Unique identification to identify a transaction, used as a reference cross-domain within the FI. Generated/Provided by the consumer. Represented by UUID without dash for compatibility purpose with openbanking.
      required: true
      schema:
        $ref: "#/components/schemas/InstructionIdentification"
    OriginalId:
      name: originalEndToEndBusinessIdentification
      in: path
      description: |
        Original endToEndBusinessIdentification passed during the send operation. It is internal to NBC or OS (does not represent Interac Payment Reference Number that is used as a parameter in the URI).
      required: true
      schema:
        $ref: "#/components/schemas/InstructionIdentification"
    ChannelId:
      in: header
      name: x-channel-id
      description: >-
        The application source code.
      schema:
        type: string
        minLength: 4
        maxLength: 10
      required: true
      example: OSFIN
    MessageId:
      name: messageId
      in: path
      description: Point to point reference, as assigned by the instructing party, and sent to the next party in the chain to unambiguously identify the message. The instructing party has to make sure that MessageIdentification is unique per instructed party for a pre-agreed period. It is use as a correlation id.
      schema:
        type: string
        format: uuid
      required: true
      example: 3bcdf7fa-7e48-4565-9751-d8acbdb64d8b
    ChannelType:
      in: header
      name: x-channel-type
      description: The application source type. Indicates if the channel is web, mobile or batch.
      schema:
        $ref: '#/components/schemas/ChannelType'
      required: true
      example: "MOBILE"
    ClientId:
      in: header
      name: x-client-id
      description: The unique system identification number of the client.
      schema:
        type: string
      required: true
      example: 30BD2E5746C6013778F4CD84D4533FAE6CF80C8C6F52C686691562F452D5FAZ0
    ClientAgentId:
      in: header
      name: x-client-agent-id
      description: The bncId or the OS id of the authenticated employee performing an action on behalf of his employer, who is a commercial client of the bank.
      schema:
        $ref: '#/components/schemas/ClientAgentId'
    AgentId:
      in: header
      name: x-agent-id
      description: The shortid of the agent that initiate the payment for on behalf
      schema:
        $ref: '#/components/schemas/AgentId'
    Accept:
      in: header
      name: accept
      required: true
      schema:
        $ref: '#/components/schemas/Accept'
      description: >-
        Header used by the gateway for routing between versions of the endpoint. Explicitly added for clarity.
    Traceparent:
      name: traceparent
      in: header
      description: |
        HTTP header containing information about the incoming request in a distributed tracing system.
        The traceparent header uses the version-trace_id-parent_id-trace_flags format where:
        - version is always 00.
        - trace_id is a hex-encoded trace id.
        - span_id is a hex-encoded span id.
        - trace_flags is a hex-encoded 8-bit field that contains tracing flags such as sampling, trace level, etc.
      required: true
      schema:
        type: string
        minLength: 1
        maxLength: 55
        example: "00-80e1afed08e019fc1110464cfa66635c-7a085853722dc6d2-01"
    Bncbusinesstraceid:
      name: bncBusinessTraceId
      in: header
      required: true
      schema:
        $ref: '#/components/schemas/Bncbusinesstraceid'
      description: >-
        HTTP header is to provide additional vendor-specific trace business identification information across different distributed systems.  This reference contains a Universally Unique IDentifier (UUID) compliant with version 4 of standard RFC4122.
    Tracestate:
      in: header
      name: tracestate
      required: false
      schema:
        $ref: '#/components/schemas/Tracestate'
      description: >-
        HTTP header is to provide additional vendor-specific trace identification information across different distributed tracing systems and is a companion header for the traceparent field. It also conveys information about the request’s position in multiple distributed tracing graphs.

        Ref: https://www.w3.org/TR/trace-context/#tracestate-header, https://www.w3.org/TR/trace-context/#tracestate-limits

    RequestId:
      in: header
      name: x-request-id
      description: >-
        Unique ID generated for each request used for message tracking purposes.
        Technical and unique traceability identifier. Used by monitoring and log tolls such as Datadog and Splunk.
      schema:
        type: string
        format: uuid
      required: true
      example: 3bcdf7fa-7e48-4565-9751-d8acbdb64d8b

  responses:
    202-accepted:
      description: Accepted
      # if we dont add a response content here, open-api will put <Void> as return value
      # in the controller, thus not enabling us to return an <Errors> object in case of
      # an error.
      content:
        application/vnd.ca.bnc.pmt+json;version=v1:
          schema:
            type: object
    204-no-content:
      description: OK - No Content
    400-bad-request:
      description: Bad Request - Validation Errors
      content:
        application/vnd.ca.bnc.pmt+json;version=v1:
          schema:
            $ref: "#/components/schemas/Errors"
    404-not-found:
      description: Resource Not Found
    403-forbidden:
      description: Forbidden
    500-internal-server-error:
      description: Internal Server Error
      content:
        application/vnd.ca.bnc.pmt+json;version=v1:
          schema:
            $ref: "#/components/schemas/Errors"
    503-service-unavailable:
      description: Service Unavailable
      content:
        application/vnd.ca.bnc.pmt+json;version=v1:
          schema:
            $ref: "#/components/schemas/Errors"
  schemas:
    Bncbusinesstraceid:
      type: string
      format: uuid-4
      example: '3bcdf7fa-7e48-4565-9751-d8acbdb64d8b'
      minLength: 36
      maxLength: 36

    Tracestate:
      type: string
      minLength: 1
      maxLength: 512
      example: 'vendorname1=opaqueValue1,vendorname2=opaqueValue2'
    Accept:
      type: string
      example: 'application/vnd.ca.bnc.pmt+json;version=v1'

    SendPaymentExecuteRequest:
      type: object
      required:
        - FIToFICustomerCreditTransfer
      properties:
        FIToFICustomerCreditTransfer:
          $ref: '#/components/schemas/FIToFICustomerCreditTransferV08'

    AccountIdentification4Choice:
      description: >-
        Account identification wrapper element.
      type: object
      required:
        - other
      properties:
        other:
          $ref: '#/components/schemas/GenericAccountIdentification1'

    ActiveCurrencyAndAmount:
      type: object
      required:
        - amount
        - currency
      properties:
        amount:
          $ref: '#/components/schemas/ActiveAmount_SimpleType'
        currency:
          $ref: '#/components/schemas/ActiveCurrencyCode'

    ActiveAmount_SimpleType:
      description: >-
        Payment's amount
      type: number
      #format: decimal  # fractionDigits=5, totalDigits=18, minInclusive=0
      minimum: 0
      example: 44.44
      x-precision: 2

    ActiveCurrencyCode:
      description: >-
        Payment's currency code
      type: string
      enum:
        - CAD
      x-example: CAD

    CashAccount38:
      description: >-
        Data block that contains debtor/sender account information.
      type: object
      required:
        - identification
      properties:
        identification:  # Id
          $ref: '#/components/schemas/AccountIdentification4Choice'

    DebtorContact:
      description: >-
        This block can contain additional contact details for the debtor.
      type: object
      properties:
        mobileNumber: # MobNb
          $ref: '#/components/schemas/DebtorPhoneNumber'
        emailAddress: # EmailAdr
          description: >-
            Optional (usage : example fintech). Debtor's email address
          type: string
          minLength: 1
          maxLength: 2048
          format: email
          example: <EMAIL>

    CreditorContact:
      description: >-
        Optional for ANR et Instant payment<br>
        Not required for Payment money request<br>
        Required for Regular and auto-deposit payment<br>
        Set of elements used to indicate how to contact the party.<br>
        The elements in this block are used to specify the additional creditor/recipient details such as email and mobile phone number.<br>
        Conditional usage: If PaymentTypeInformation.LocalInstrument.Proprietary is FULFILL_REQUEST_FOR_PAYMENT then this block is not required.<br>
        If PaymentTypeInformation.LocalInstrument.Proprietary is ACCOUNT_DEPOSIT_PAYMENT or REALTIME_ACCOUNT_DEPOSIT_PAYMENT then this block is optional.<br>
        In all other cases either the Creditor.Name plus the Creditor.ContactDetails must be provided.
      type: object
      properties:
        mobileNumber: # MobNb
          $ref: '#/components/schemas/CreditorPhoneNumber'
        emailAddress: # EmailAdr
          description: >-
            For all payment type, except Fulfill money request<br>
            Identifies the creditor's address for electronic mail (e-mail).<br>
            Creditor's/recipient's mobile phone number.<br>
            Conditional: when the ContactDetails block is present, at least one of the elements, mobile phone or email must be specified.
          type: string
          minLength: 1
          maxLength: 2048
          format: email
          example: <EMAIL>

    CreditTransferTransaction39:
      description: >-
        Set of elements providing information specific to the individual credit transfer(s).
      type: object
      required:
        - paymentIdentification
        - paymentTypeInformation
        - interbankSettlementAmount
        - debtor
        - debtorAccount
        - supplementaryData
      properties:
        paymentIdentification:  # PmtId
          $ref: '#/components/schemas/PaymentIdentification7'
        paymentTypeInformation: # PmtTpInf
          $ref: '#/components/schemas/PaymentTypeInformation28'
        interbankSettlementAmount: # IntrBkSttlmAmt
          $ref: '#/components/schemas/ActiveCurrencyAndAmount'
        interbankSettlementDate: # IntrBkSttlmDt
          $ref: '#/components/schemas/ISODate'
        debtor: # Dbtr
          $ref: '#/components/schemas/DebtorIdentification'
        debtorAccount: # DbtrAcct
          $ref: '#/components/schemas/CashAccount38'
        creditor: # Cdtr
          $ref: '#/components/schemas/CreditorIdentification'
        creditorAccount: # CdtrAcct
          $ref: '#/components/schemas/CashAccount38'
        remittanceInformation: # RmtInf
          $ref: '#/components/schemas/RemittanceInformation16'
        mandateRelatedInformation:
          $ref: '#/components/schemas/MandateRelatedInformation'
        supplementaryData:
          $ref: '#/components/schemas/SupplementaryData'

    Errors:
      type: object
      description: Returns a list of errors.
      properties:
        errors:
          type: array
          items:
            $ref: '#/components/schemas/Error'

    Error:
      description: Error code.
      type: object
      title: Error
      required:
        - code
        - text
        - origin
      properties:
        code:
          description: Error Source Code.
          title: Code
          type: string
          maxLength: 50
          example: "TECHNICAL_ERROR"
        text:
          description: Description.
          title: Text
          type: string
          maxLength: 2000
          example: "technical error"
        origin:
          description: Error source.
          title: Origin
          type: string
          maxLength: 55
          example: "pmt_domestic_interac_etransfer_api"
        rule:
          description: Business rule.
          title: Rule
          type: string
          maxLength: 50
          example: "R_EAPI_003"

    FIToFICustomerCreditTransferV08:
      type: object
      required:
        - groupHeader
        - creditTransferTransactionInformation
      properties:
        groupHeader:  # GrpHdr
          $ref: '#/components/schemas/GroupHeader93'
        creditTransferTransactionInformation: # CdtTrfTxInf
          $ref: '#/components/schemas/CreditTransferTransaction39'

    GenericAccountIdentification1:
      type: object
      required:
        - identification
      properties:
        identification:  # Id
          description: >-
            The account number.<br>
            Usage: Valid format: aaa-bbbbb-cccccccccccccccccccccccc where<br>
            123 is the Institution Id (fixed length 3 digits)<br>
            bbbbb is the Transit Number (fixed length 5 digits)<br>
            cccccccccccccccccccccccc is the bank account number (up to max 24 digits)
          type: string
          minLength: 1
          maxLength: 34
          example: aaa-bbbbb-cccccccccccc

    GenericOrganisationIdentification1:
      type: object
      required:
        - identification
      properties:
        identification:  # Id
          description: >-
            Usage: This must contain the identification of the Debtor by their customer id as was set by their FI (BNCID or ID for Fintech )
          type: string
          minLength: 1
          maxLength: 128

    GroupHeader93:
      description: >-
        Set of characteristics shared by all individual transactions included in the message.
      type: object
      properties:
        messageIdentification:  # MsgId
          description: >-
            Point to point reference, as assigned by the instructing party, and sent to the next party in the chain to unambiguously identify the message.<br/><br/>
            Usage: The instructing party has to make sure that MessageIdentification is unique per instructed party for a pre-agreed period.<br/><br/>
            A new id message for each request.
          type: string
          minLength: 1
          maxLength: 35
          example: d04273e9014645c2b12e3ef18ef8589c
        creationDateTime: # CreDtTm
          description: >-
            Date and time at which the message was created.
          type: string
          format: date-time
          example: "2019-05-05T17:29:12.123Z"
        numberOfTransactions: # NbOfTxs
          description: >-
            Number of individual transactions contained in the message. Usage: Expected value is 1
          type: string
          pattern: '[0-9]{1,15}'
      required:
        - messageIdentification
        - creationDateTime
        - numberOfTransactions

    ISODate:
      type: string
      format: date
      description: >-
        Requested execution date and time for payment request. A particular
        point in the progression of time in a calendar year expressed in the YYYY-MM-DD
        format. This representation is defined in XML Schema Part 2 Datatypes Second
        Edition - W3C Recommendation 28 October 2004 which is aligned with ISO 8601.
        </br></br> Should be filled with today's date. Format: YYYY-MM-DD
      example: '2020-01-23'

    LocalInstrument2Choice:
      type: object
      properties:
        proprietary:
          description: >-
            Type of payment </br>
            REGULAR_PAYMENT                  - Regular "Send-Money" payment </br>
            FULFILL_REQUEST_FOR_PAYMENT      - Fulfill Money Request payment </br>
            ACCOUNT_ALIAS_PAYMENT            - Auto-Deposit payment </br>
            REALTIME_ACCOUNT_ALIAS_PAYMENT   - Real-time Auto-Deposit payment </br>
            ACCOUNT_DEPOSIT_PAYMENT          - Account-Deposit payment </br>
            REALTIME_ACCOUNT_DEPOSIT_PAYMENT - Real-time Account-Deposit payment </br>
          type: string
          enum:
            - REGULAR_PAYMENT
            - FULFILL_REQUEST_FOR_PAYMENT
            - ACCOUNT_ALIAS_PAYMENT
            - REALTIME_ACCOUNT_ALIAS_PAYMENT
            - ACCOUNT_DEPOSIT_PAYMENT
            - REALTIME_ACCOUNT_DEPOSIT_PAYMENT
          x-example: REGULAR_PAYMENT
      required:
        - proprietary

    OrganisationIdentification29:
      type: object
      properties:
        other: # Othr
          type: array
          items:
            $ref: '#/components/schemas/GenericOrganisationIdentification1'
      required:
        - other
    Party38Choice:
      type: object
      description: This data block identifies the Debtor by their customer id at their financial institution
      properties:
        organisationIdentification:
          $ref: '#/components/schemas/OrganisationIdentification29'
      required:
        - organisationIdentification
    DebtorIdentification:
      type: object
      description: >-
        The party which owes the money to the (ultimate) creditor. Usage: This data block must identify the debtor/sender
      properties:
        name:  # Nm
          type: string
          minLength: 1
          maxLength: 140
          description: >-
            Name by which a party is known and which is usually used to identify that party. Optional (usage : example fintech)
        identification: # Id
          $ref: '#/components/schemas/Party38Choice'
        contactDetails: # CtctDtls
          $ref: '#/components/schemas/DebtorContact'
    CreditorIdentification:
      type: object
      description: >-
        For all payment types, except Fulfill money request<br>
        Conditional usage: If PaymentTypeInformation.LocalInstrument.Proprietary is FULFILL_REQUEST_FOR_PAYMENT then this element is not required. In all other cases either the Creditor.Name plus the Creditor.ContactDetails must be provided
      properties:
        name:  # Nm
          type: string
          minLength: 1
          maxLength: 140
          description: >-
            Name of the creditor/recipient. When present, this will be used in the notifications sent to the Creditor.<br>
            Conditional usage: If PaymentTypeInformation.LocalInstrument.Proprietary is FULFILL_REQUEST_FOR_PAYMENT then this element is not required. In all other cases either the Creditor.Name plus the Creditor.ContactDetails must be provided
        contactDetails: # CtctDtls
          $ref: '#/components/schemas/CreditorContact'
    PaymentAuthentication:
      description: >-
        Required only if paymentType is REGULAR_PAYMENT. </br>
        Data block containing information regarding the authentication that will be required to complete this transfer.
      type: object
      required:
        - securityQuestion
        - securityAnswer
        - hashType
        - hashSalt
      properties:
        securityQuestion:
          description: >-
            Security question text.
          type: string
          minLength: 1
          maxLength: 40
        securityAnswer:
          description: >-
            Security answer encrypted. Answer to the security question (as provided by the customer) with leading and trailing whitespace trimmed, uppercased, postfixed with hashSalt if present,
            hashed using the alghoritm identified by hashType and then Base64 encoded. ISO-8859-1 encoding to be used when the hash is generated.
            This will only be present if the authenticationRequired field (received from the get operation) indicates that validation is required.
          type: string
          minLength: 3
          maxLength: 64
        hashType:
          description: >-
            Alghorithm used to hash the security answer. It has to be one of values supported by the system. Required if authenticationRequired is true.
          type: string
          enum: ['SHA2']
        hashSalt:
          description: >-
            Salt used to hash the security answer. Required if authenticationRequired is true. Hash salt to strengthen encryption.
          type: string
          minLength: 1
          maxLength: 44
    PaymentIdentification7:
      type: object
      required:
        - endToEndIdentification
      properties:
        instructionIdentification:  # InstrId
          description: >-
            The unique business transaction ID.<br/></br>
            Consumer generated transaction identifier.
          type: string
          maxLength: 35
        endToEndIdentification: # EndToEndId
          description: >-
            EndToEndIdentification is a transaction identifier used across BNC or OS systems (end-to-end chain). It is internal to BNC or OS.
          type: string
          maxLength: 35
    PaymentTypeInformation28:
      type: object
      properties:
        localInstrument: # LclInstrm
          $ref: '#/components/schemas/LocalInstrument2Choice'
      required:
        - localInstrument
    DebtorPhoneNumber:
      type: string
      pattern: \+[0-9]{1,3}-[0-9()+\-]{1,30}
      description: >-
        Optional (usage : example fintech). Debtor's mobile phone number </br>
        The collection of information which identifies a specific phone or FAX number as defined by telecom services.
        It consists of a "+" followed by the country code (from 1 to 3 characters)
        then a "-" and finally, any combination of numbers, "(", ")", "+" and "-"
        (up to 30 characters).
      minLength: 1
      maxLength: 30
      example: ******-555-1212
    CreditorPhoneNumber:
      type: string
      pattern: \+[0-9]{1,3}-[0-9()+\-]{1,30}
      description: >-
        For all payment type, except Fulfill money request<br>
        Identifies the creditor's mobile phone number.<br>
        Creditor's/recipient's mobile phone number.<br>
        Conditional: when the ContactDetails block is present, at least one of the elements, mobile phone or email must be specified.<br>
        The collection of information which identifies a specific phone or FAX numberas defined by telecom services.<br>
        It consists of a "+" followed by the country code (from 1 to 3 characters)
        then a "-" and finally, any combination of numbers, "(", ")", "+" and "-"
        (up to 30 characters).</br>
      minLength: 1
      maxLength: 30
      example: ******-555-1212
    RemittanceInformation16:
      type: object
      properties:
        unstructured:  # Ustrd
          type: array
          items:
            type: string
            minLength: 1
            maxLength: 140
          description: Remittance information in an unstructured form. Up to 3 elements
            allowed for a total of 420 characters. Every part of the remittance information
            must be between 1 to 140 characters long and match the
            regex ^((?!(#|\&|\\|%|\<|\>|http\:|https\:|www|function|return|javascript|select|drop|truncate)).)*$
          maxItems: 3
          x-code-size: 2001
          #structured: # Strd
          #type: array
          #items:
          #$ref: '#/components/schemas/StructuredRemittanceInformation16'
          #description: Remittance information in a structured form. Up to 5 block
          #allowed.
          #maxItems: 5
          #x-code-size: 2002
    SupplementaryData:
      description: >-
        Additional information that cannot be captured in the structured elements and/or any other specific block.
      type: object
      required:
        - fraudSupplementaryInfo
        - clientType
      properties:
        creditorPreferredLanguage:
          description: >-
            Creditor's preferred communication language for notifications
          type: string
          enum:
            - EN
            - FR
          example: EN
        interacMoneyRequestId:
          description: >-
            Only for payment of money request </br>
            Interac Money Request ID
          type: string
          minLength: 1
          maxLength: 35
        accountHolderName:
          description: >-
            The debtor account holder name. If empty, the debtor name will be passed to Interac.
          type: string
          minLength: 1
          maxLength: 80
        creditorAutoDepositRegNumber:
          description: >-
            Creditor Account alias Registration reference number generated by Interac, for using at payment initiation for the Auto deposit Send. It's mandatory if payment type is 'ACCOUNT_ALIAS_PAYMENT' or 'REALTIME_ACCOUNT_ALIAS_PAYMENT'
          type: string
          minLength: 1
          maxLength: 35
        fraudSupplementaryInfo:
          $ref: '#/components/schemas/SupplementaryInfo'
        paymentAuthentication:
          $ref: '#/components/schemas/PaymentAuthentication'
        clientType:
          type: string
          enum:
            - INDIVIDUAL
            - ORGANIZATION
          description:
            The client type to describe whether it is an individual or an organization.
          example: INDIVIDUAL
        creditorId:
          type: string
          format: uuid
          description:
            The id of the recipient.
          example: 95D2E46D-DC12-4510-BFEA-B5C268A206B2
        confirmationId:
          type: string
          description: The short id of the payment to display to the end user.
          maxLength: 36
          example: 1234567ZY
    MandateRelatedSupplementaryData:
      description: Additional data required for the deferred transactions
      type: object
      required:
        - numberOfRemaining
        - currentOccurrence
        - originalChannelId
        - originalChannelType
      properties:
        numberOfRemaining:
          type: integer
          format: int32
          description: Number of deferred remaining
          minimum: 0
        currentOccurrence:
          type: integer
          format: int32
          description: Current payment number in process
          minimum: 1
        originalChannelType:
          type: string
          enum:
            - WEB
            - MOBILE
            - BATCH
          example: WEB
        originalChannelId:
          type: string
          description: Original Channel Identifier
          minLength: 4
          maxLength: 10
          example: OSFIN
    SupplementaryInfo:
      description: Additional data block required for Fraud or Compliance reasons.
        All these 5 elements are required. Any conditional exceptions must be discussed/established
        with the Interac Fraud team prior to implementation.
      type: object
      required:
        - clientIpAddress
        - clientDeviceFingerPrint
        - clientAuthenticationMethod
      properties:
        clientIpAddress:
          description: Public IP Address used by the Customer during payment initiation.
            This address must match the regex ^[a-fA-F0-9. :]*$
          type: string
          minLength: 2
          maxLength: 64
          example: *******
        clientCardNumber:
          description: >-
            Sender's card number associated with the account, HASHED. Supported hash algorithm: SHA256
          type: string
          pattern: '^[\w\p{L}\p{Mn}.,''\-\/ ]*$'
          minLength: 1
          maxLength: 64
          example: ****************
        clientDeviceFingerPrint:
          description: >-
            Unique device fingerprint (the session id). The following options/types are supported in
            this preferred priority. They must start with a prefix (ITM or FTM or
            UID or CID) followed by the value of the ************* session id/device
            id/cookie id
              Interac ThreatMetrix profiling session Id        - ITM*************
              FI ThreatMetrix profiling Session Id             - FTM*************
              Unique Device Identifier of device               - UID*************
              Cookie Id Placed at customers computer or device - CID**************
          type: string
          pattern: '^[\w\p{L}\p{Mn}.,''\-\/ ]*$'
          minLength: 1
          maxLength: 256
          example: ITM1234567890123
        clientAuthenticationMethod:
          description: >-
            Authentication method option used to authenticate the customer (sender)
            prior to payment initiation.
            The following values are currently supported.
          type: string
          enum:
            - PASSWORD
            - PVQ
            - FINGERPRINT
            - BIO_METRICS
            - OTP
            - TOKEN
            - MAKER_CHECKER
            - NONE
            - OTHER
          x-example: PASSWORD
        accountCreationDate:
          description: >-
            Date at which the account was created.
          type: string
          format: date
          example: "2022-01-23"

    GenericAccountIdentification:
      type: object
      required:
        - identification
      properties:
        identification: # Id
          description: >-
            The account number.<br>
            Usage: Valid format: aaa-bbbbb-cccccccccccccccccccccccc where<br>
            123 is the Institution Id (fixed length 3 digits)<br>
            bbbbb is the Transit Number (fixed length 5 digits)<br>
            cccccccccccccccccccccccc is the bank account number (up to max 24 digits)
          type: string
          minLength: 1
          maxLength: 34
          example: aaa-bbbbb-cccccccccccc
    AccountIdentificationChoice:
      description: >-
        Account identification wrapper element.
      type: object
      required:
        - other
      properties:
        other:
          $ref: '#/components/schemas/GenericAccountIdentification'
    CreditorAccount:
      description: >-
        Data block that contains debtor/sender account information.
      type: object
      required:
        - identification
      properties:
        identification: # Id
          $ref: '#/components/schemas/AccountIdentificationChoice'

    MandateRelatedInformation:
      type: object
      required:
        - mandateIdentification
        - frequencyTypePeriod
        - countPerPeriod
        - supplementaryData
      properties:
        mandateIdentification:
          type: string
          description: Unique deferred identifier
          minLength: 1
          maxLength: 36
        frequencyTypePeriod:
          type: string
          description: >-
            Period of the recurring transfer.
            DAIL = Daily - Event takes place every day.
            MNTH = Monthly - Event takes place every month or once a month.
            WEEK = Weekly - Event takes place once a week.
            TWMN = TwiceAMonth - Event takes place two times a month.
            TOWK = EveryTwoWeeks - Event takes place every two weeks.
            ONCE = Once - Event takes place once.
            EOFM = End Of Month - Event takes place on the last day of every months.
            Enum: [ MNTH, WEEK, DAIL, TWMN, TOWK, ONCE, EOFM ]
          enum:
            - MNTH
            - WEEK
            - DAIL
            - TWMN
            - TOWK
            - ONCE
            - EOFM
        countPerPeriod:
          type: integer
          format: int32
          description: >-
            Number of payments total
            Example : 1 for a single deferred and more than 1 for a series of deferred
          minimum: 1
          example: 17
        supplementaryData:
          $ref: '#/components/schemas/MandateRelatedSupplementaryData'

    ChannelType:
      type: string
      description: >-
        The application source type. Indicates if the channel is web or mobile.
      enum:
        - WEB
        - MOBILE
        - BATCH
    ClientAgentId:
      type: string
      minLength: 1
      maxLength: 64
      example : "30BD2E5746C6013778F4CD84D4533FAE6CF80C8C6F52C686691562F452D5FAZ0"
    AgentId:
      type: string
      pattern: '^[\w\p{L}\p{Mn}.,\-\/]*$'
      example: ourk020
    InstructionIdentification:
      description: Unique identification as assigned by an instructing party for an instructed party to unambiguously identify the instruction. The instruction identification is a point to point reference that can be used between the instructing party and the instructed party to refer to the individual instruction. It can be included in several messages related to the instruction.
      type: string
      maxLength: 35
      example: "5a6fad7df3fe4e3481a096f48c818ab3"
  # SECURITY: Defines the security schemes used in the API
  securitySchemes:
    oAuth2:
      type: oauth2
      description: This API uses OAuth 2 with the implicit grant flow.
      flows:
        authorizationCode: # ou un autre flux OAuth2 comme implicit, password, clientCredentials
          authorizationUrl: https://api-ti.bnc.ca/bnc/ti-out/sso/oauth2/ausb2snfqxccuEfI90h7/v1/authorize
          tokenUrl: https://api-ti.bnc.ca/bnc/ti-out/sso/oauth2/ausb2snfqxccuEfI90h7/v1/token
          scopes:
            pmt:simulation-processing:update: update resource
