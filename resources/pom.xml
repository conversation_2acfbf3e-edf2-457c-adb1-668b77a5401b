<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>ca.bnc.payment</groupId>
        <artifactId>pmt-etransfer-payment-processing-simulation-api</artifactId>
        <version>0.1.4-SNAPSHOT</version>
    </parent>

    <artifactId>pmt-etransfer-payment-processing-simulation-api-resources</artifactId>
    <name>pmt-etransfer-payment-processing-simulation-api (resources)</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>pmt-etransfer-payment-processing-simulation-api-resources</finalName>

        <plugins>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>${openapi-generator-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>PaymentProcessingSimulationApi</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/specification/pmt-etransfer-payment-processing-simulation-api.yaml
                            </inputSpec>
                            <generatorName>spring</generatorName>
                            <apiPackage>${default-package}.${project.artifactId}.generated.rest</apiPackage>
                            <modelPackage>${default-package}.${project.artifactId}.generated.model</modelPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <ignoreFileOverride>${project.basedir}/.openapi-codegen-ignore</ignoreFileOverride>
                            <configOptions>
                                <containerDefaultToNull>true</containerDefaultToNull>
                                <interfaceOnly>true</interfaceOnly>
                                <serializableModel>true</serializableModel>
                                <skipDefaultInterface>true</skipDefaultInterface>
                                <useSpringBoot3>true</useSpringBoot3>
                                <useBeanValidation>true</useBeanValidation>
                            </configOptions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-remote-resources-plugin</artifactId>
                <version>${maven-remote-resources-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>bundle</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <includes>
                        <include>**/*.yaml</include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>${jib-maven-plugin.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
